import type { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { StructuredOutputParser } from '@langchain/core/output_parsers';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { RunnableSequence } from '@langchain/core/runnables';
import { z } from 'zod';

import type { Job } from '../../entities/job';
import type { Resume } from '../../entities/resume';

/**
 * Represents a title match with confidence score and reasoning
 */
export interface TitleMatch {
  rankedTitles: Array<{ title: string; rank: number }>;
  pickedTitle: { value: string; reasoning: string };
}

/**
 * Error class for title matching operations
 */
export class TitleMatchError extends Error {
  constructor(
    message: string,
    public readonly code:
      | 'AI_UNAVAILABLE'
      | 'TIMEOUT'
      | 'INVALID_INPUT'
      | 'NO_MATCHES'
  ) {
    super(message);
    this.name = 'TitleMatchError';
  }
}

const titleAnalysisSchema = z.object({
  rankedTitles: z
    .array(
      z.object({
        title: z.string(),
        rank: z.number().min(1).max(4),
      })
    )
    .describe(
      'Array of ranked professional titles in comparison to the job requirements, best match first'
    ),
  pickedTitle: z
    .object({
      value: z.string(),
      reasoning: z.string(),
    })
    .describe(
      'The final selected title and the reasoning for why it was chosen'
    ),
});

/**
 * Analyzes job title matches between resume variations and a job posting
 */
export class TitleMatchAnalyzer {
  constructor(private model: BaseChatModel) {}

  /**
   * Finds the best matching job title from resume variations
   * @param resumeVariations Array of resume variations
   * @param job Job posting to match against
   * @param options Optional configuration
   * @returns Promise resolving to the best title match
   */
  async findBestTitleMatch(
    resumeVariations: [Resume, ...Resume[]],
    job: Job,
    options: { timeoutMs?: number } = {}
  ): Promise<TitleMatch> {
    try {
      // Extract titles from resume variations
      const resumeTitles = this.extractResumeTitles(resumeVariations);

      // Handle case where no titles are found
      if (resumeTitles.length === 0) {
        throw new TitleMatchError(
          'No job titles found in resume variations',
          'INVALID_INPUT'
        );
      }

      // Create prompt template
      const prompt = ChatPromptTemplate.fromTemplate(`
        You are an expert job title optimizer. Analyze these resume titles against the job posting:

        Resume titles: {resumeTitles}
        Job title: {jobTitle}

        First, rank all titles from best (1) to worst match. For each title:
        - Preserve the original wording but allow minor improvements
        - Consider role, seniority, and industry fit
        - Assign a rank number (4=best, 1=worst)

        Then select one title to recommend, which can be:
        - The top-ranked title exactly as-is
        - A slightly improved version of the top title
        - A hybrid of the best elements from multiple titles

        Your output must include:
        1. Ranked list of all titles with ranks
        2. Final selected title with brief reasoning
        3. For the selected title, explain any modifications made

        Important rules:
        - Never invent completely new titles
        - Preserve the candidate's original wording when possible
        - Maximum 2-3 word modifications if needed

        {format_instructions}
      `);

      // Create parser
      const parser = StructuredOutputParser.fromZodSchema(titleAnalysisSchema);

      // Create chain
      const chain = RunnableSequence.from([prompt, this.model, parser]);

      // Invoke chain with timeout if specified
      const result = options.timeoutMs
        ? await this.withTimeout(
            chain.invoke({
              resumeTitles: resumeTitles,
              jobTitle: job.title,
              format_instructions: parser.getFormatInstructions(),
            }),
            options.timeoutMs,
            'Title matching analysis'
          )
        : await chain.invoke({
            resumeTitles: resumeTitles,
            jobTitle: job.title,
            format_instructions: parser.getFormatInstructions(),
          });

      return {
        rankedTitles: result.rankedTitles,
        pickedTitle: {
          value: result.pickedTitle.value,
          reasoning: result.pickedTitle.reasoning,
        },
      };
    } catch (error) {
      // Handle known errors
      if (error instanceof TitleMatchError) {
        throw error;
      }

      // Handle timeout errors
      if (error instanceof Error && error.message.includes('timeout')) {
        throw new TitleMatchError(
          'Title matching analysis timed out',
          'TIMEOUT'
        );
      }

      // Handle AI unavailability
      if (error instanceof Error && error.message.includes('AI unavailable')) {
        throw new TitleMatchError(
          'AI model is unavailable for title matching',
          'AI_UNAVAILABLE'
        );
      }

      // Handle unexpected errors
      throw new TitleMatchError(
        `Unexpected error during title matching: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        'INVALID_INPUT'
      );
    }
  }

  /**
   * Extracts job titles from resume variations
   * @param resumeVariations Array of resume variations
   * @returns Array of unique job titles
   */
  private extractResumeTitles(resumeVariations: Resume[]): string[] {
    const titles = new Set<string>();

    for (const resume of resumeVariations) {
      titles.add(
        resume.header.items.find((item) => item.name === 'title')?.value ?? ''
      );
    }

    return Array.from(titles);
  }

  /**
   * Wraps a promise with a timeout
   * @param promise Promise to execute
   * @param timeoutMs Timeout in milliseconds
   * @param operationName Name of the operation for error messages
   * @returns Promise that resolves with the result or rejects with a timeout error
   */
  private async withTimeout<T>(
    promise: Promise<T>,
    timeoutMs: number,
    operationName: string
  ): Promise<T> {
    return Promise.race([
      promise,
      new Promise<T>((_, reject) =>
        setTimeout(() => {
          reject(new Error(`${operationName} timed out after ${timeoutMs}ms`));
        }, timeoutMs)
      ),
    ]);
  }
}
