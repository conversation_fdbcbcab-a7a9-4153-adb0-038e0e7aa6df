import { BaseChatModel } from '@langchain/core/language_models/chat_models.js';
import { StructuredOutputParser } from '@langchain/core/output_parsers';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { RunnableSequence } from '@langchain/core/runnables';
import { z } from 'zod';

import type { Resume } from '../../entities/resume';
import type { ConsolidatedSkill } from './types';

// Schema for skill extraction response
const skillExtractionSchema = z.object({
  skills: z
    .array(z.string())
    .describe(
      'Array of technical skills, tools, frameworks, programming languages, and technologies found in the text'
    ),
});

/**
 * Consolidates skills from multiple resume variations into a unified list.
 *
 * This class extracts skills from all resume sections (skills, work, projects)
 * and merges duplicate skills across resume variations while tracking their sources.
 */
export class SkillConsolidator {
  private model: BaseChatModel;

  constructor(model: BaseChatModel) {
    this.model = model;
  }
  /**
   * Consolidates skills from multiple resume variations.
   *
   * @param resumeVariations - Array of resume variations to consolidate skills from
   * @returns Promise that resolves to array of consolidated skills with source tracking
   */
  async consolidate(resumeVariations: Resume[]): Promise<ConsolidatedSkill[]> {
    const skillMap = new Map<string, ConsolidatedSkill>();

    for (const [resumeIndex, resume] of resumeVariations.entries()) {
      const extractedSkills = await this.extractSkillsFromResume(
        resume,
        resumeIndex
      );

      extractedSkills.forEach((skill) => {
        const normalizedName = this.normalizeSkillName(skill.name);

        if (skillMap.has(normalizedName)) {
          // Merge with existing skill
          const existing = skillMap.get(normalizedName);

          if (existing) {
            this.mergeSkills(existing, skill);
          }
        } else {
          // Add new skill with normalized name
          skillMap.set(normalizedName, {
            ...skill,
            name: normalizedName,
          });
        }
      });
    }

    return Array.from(skillMap.values());
  }

  /**
   * Extracts skills from all sections of a single resume.
   *
   * @param resume - Resume to extract skills from
   * @param resumeIndex - Index of the resume in the variations array
   * @returns Promise that resolves to array of skills found in the resume
   */
  private async extractSkillsFromResume(
    resume: Resume,
    resumeIndex: number
  ): Promise<ConsolidatedSkill[]> {
    const skills: ConsolidatedSkill[] = [];

    // Extract from skills section
    const skillsSection = resume.sections.find(
      (section) => section.name === 'skills'
    );
    if (skillsSection) {
      skillsSection.items.forEach((skill) => {
        skills.push({
          name: skill.name,
          level: skill.level || undefined,
          keywords: skill.keywords || [],
          sourceResumes: [resumeIndex],
        });

        // Also add keywords as separate skills if they exist
        if (skill.keywords) {
          skill.keywords.forEach((keyword) => {
            skills.push({
              name: keyword,
              level: undefined,
              keywords: [],
              sourceResumes: [resumeIndex],
            });
          });
        }
      });
    }

    // Combine all text from work, projects, and other sections
    const combinedText: string[] = [];

    // Extract text from work section
    const workSection = resume.sections.find(
      (section) => section.name === 'work'
    );
    if (workSection) {
      workSection.items.forEach((workItem) => {
        // Add work highlights (bullet points)
        if (workItem.highlights) {
          combinedText.push(workItem.highlights.join(' '));
        }

        // Add work summary
        if (workItem.summary) {
          combinedText.push(workItem.summary);
        }
      });
    }

    // Extract text from projects section
    const projectsSection = resume.sections.find(
      (section) => section.name === 'projects'
    );
    if (projectsSection) {
      projectsSection.items.forEach((project) => {
        // Add project keywords as text
        if (project.keywords) {
          combinedText.push(project.keywords.join(' '));
        }

        // Add project highlights
        if (project.highlights) {
          combinedText.push(project.highlights.join(' '));
        }

        // Add project description
        if (project.description) {
          combinedText.push(project.description);
        }
      });
    }

    // Extract skills from the combined text
    if (combinedText.length > 0) {
      const allText = combinedText.join(' ');
      const extractedSkills = await this.extractSkillsFromText(allText);
      extractedSkills.forEach((skillName) => {
        skills.push({
          name: skillName,
          level: undefined,
          keywords: [],
          sourceResumes: [resumeIndex],
        });
      });
    }

    return skills;
  }

  /**
   * Normalizes skill names for consistent matching.
   *
   * @param skillName - Raw skill name to normalize
   * @returns Normalized skill name
   */
  private normalizeSkillName(skillName: string): string {
    return skillName
      .toLowerCase()
      .trim()
      .replace(/[^\w\s.+-]/g, '') // Remove special characters except dots, hyphens, plus signs, and spaces
      .replace(/\s+/g, ' '); // Normalize whitespace
  }

  /**
   * Merges two skills with the same normalized name.
   *
   * @param existing - Existing consolidated skill
   * @param newSkill - New skill to merge
   */
  private mergeSkills(
    existing: ConsolidatedSkill,
    newSkill: ConsolidatedSkill
  ): void {
    // Merge source resumes
    newSkill.sourceResumes.forEach((resumeIndex) => {
      if (!existing.sourceResumes.includes(resumeIndex)) {
        existing.sourceResumes.push(resumeIndex);
      }
    });

    // Use the highest level if available
    if (
      newSkill.level &&
      (!existing.level ||
        this.compareLevels(newSkill.level, existing.level) > 0)
    ) {
      existing.level = newSkill.level;
    }

    // Merge keywords
    newSkill.keywords.forEach((keyword) => {
      const normalizedKeyword = this.normalizeSkillName(keyword);
      const existingNormalizedKeywords = existing.keywords.map((k) =>
        this.normalizeSkillName(k)
      );

      if (!existingNormalizedKeywords.includes(normalizedKeyword)) {
        existing.keywords.push(keyword);
      }
    });
  }

  /**
   * Compares skill levels to determine which is higher.
   *
   * @param level1 - First level to compare
   * @param level2 - Second level to compare
   * @returns Positive if level1 > level2, negative if level1 < level2, 0 if equal
   */
  private compareLevels(level1: string, level2: string): number {
    const levelOrder = [
      'beginner',
      'intermediate',
      'advanced',
      'expert',
      'master',
    ];
    const index1 = levelOrder.indexOf(level1.toLowerCase());
    const index2 = levelOrder.indexOf(level2.toLowerCase());

    // If levels are not in our known order, treat them as equal
    if (index1 === -1 || index2 === -1) {
      return 0;
    }

    return index1 - index2;
  }

  /**
   * Extracts potential skill names from free text using AI.
   * Uses an LLM to identify technical skills, tools, frameworks, and technologies.
   *
   * @param text - Text to extract skills from
   * @returns Promise that resolves to array of potential skill names found in the text
   */
  private async extractSkillsFromText(text: string): Promise<string[]> {
    if (!text.trim()) {
      return [];
    }

    try {
      const result = await this.createSkillExtractor().invoke({ text });
      return result.skills || [];
    } catch (error) {
      console.warn(
        'Failed to extract skills using AI, falling back to pattern matching:',
        error
      );
      return this.extractSkillsFromTextFallback(text);
    }
  }

  /**
   * Creates a langchain runnable for skill extraction.
   */
  private createSkillExtractor() {
    const parser = StructuredOutputParser.fromZodSchema(skillExtractionSchema);

    const prompt = ChatPromptTemplate.fromTemplate(`
You are an expert at identifying technical skills from resume and job description text.

Extract all technical skills, programming languages, frameworks, tools, databases, cloud platforms, and technologies mentioned in the following text.

Focus on:
- Programming languages (JavaScript, Python, Java, etc.)
- Web frameworks and libraries (React, Angular, Vue, Express, etc.)
- Databases (MySQL, PostgreSQL, MongoDB, etc.)
- Cloud platforms and services (AWS, Azure, Google Cloud, etc.)
- Development tools (Git, Docker, Kubernetes, etc.)
- Software and applications (Figma, Jira, Photoshop, etc.)

Be specific and extract the exact names as they appear. Do not include soft skills or general concepts.

Text to analyze:
{text}

{format_instructions}

Respond ONLY with the structured JSON output.`);

    return RunnableSequence.from([
      {
        text: (input: { text: string }) => input.text,
        format_instructions: () => parser.getFormatInstructions(),
      },
      prompt,
      this.model,
      parser,
    ]);
  }

  /**
   * Fallback method using pattern matching when AI extraction fails.
   * This is the original implementation.
   *
   * @param text - Text to extract skills from
   * @returns Array of potential skill names found in the text
   */
  private extractSkillsFromTextFallback(text: string): string[] {
    const skills: string[] = [];

    // Common technology patterns to look for
    const techPatterns = [
      // Programming languages
      /\b(JavaScript|TypeScript|Python|Java|C\+\+|C#|PHP|Ruby|Go|Rust|Swift|Kotlin|Scala|R|MATLAB)\b/gi,
      // Web technologies
      /\b(React|Vue|Angular|Node\.js|Express|Django|Flask|Laravel|Spring|ASP\.NET)\b/gi,
      // Databases
      /\b(MySQL|PostgreSQL|MongoDB|Redis|SQLite|Oracle|SQL Server|DynamoDB|Cassandra)\b/gi,
      // Cloud platforms
      /\b(AWS|Azure|Google Cloud|GCP|Docker|Kubernetes|Terraform|Jenkins)\b/gi,
      // Tools and frameworks
      /\b(Git|GitHub|GitLab|Jira|Confluence|Slack|Figma|Adobe|Photoshop|Illustrator)\b/gi,
    ];

    techPatterns.forEach((pattern) => {
      const matches = text.match(pattern);
      if (matches) {
        matches.forEach((match) => {
          const normalized = match.trim();
          if (normalized && !skills.includes(normalized)) {
            skills.push(normalized);
          }
        });
      }
    });

    return skills;
  }
}
