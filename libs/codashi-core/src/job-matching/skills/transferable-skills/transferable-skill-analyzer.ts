import type { BaseChatModel } from '@langchain/core/language_models/chat_models';

import type { Job } from '../../../entities/job';
import type {
  ConsolidatedSkill,
  SkillMatchOptions,
  TransferableSkillMatch,
} from '../types';
import { TransferableSkillExtractor } from './transferable-skill-extractor';

/**
 * Analyzes transferable skills using AI reasoning to identify similar skills
 * with confidence ratings between resume skills and job requirements.
 *
 * @deprecated This class is now a wrapper around TransferableSkillExtractor.
 * Consider using TransferableSkillExtractor directly for new code.
 */
export class TransferableSkillAnalyzer {
  private readonly extractor: TransferableSkillExtractor;

  constructor(private readonly model: BaseChatModel) {
    this.extractor = new TransferableSkillExtractor(model);
  }

  /**
   * Analyzes transferable skills between unmatched resume skills and job requirements
   *
   * @param unmatchedResumeSkills - Resume skills that didn't have direct matches
   * @param unmatchedJobSkills - Job skills that didn't have direct matches
   * @param options - Configuration options for the analysis
   * @returns Promise resolving to array of transferable skill matches
   */
  async analyzeTransferableSkills(
    unmatchedResumeSkills: ConsolidatedSkill[],
    unmatchedJobSkills: Job['skills'],
    options: SkillMatchOptions = {}
  ): Promise<TransferableSkillMatch[]> {
    return this.extractor.analyzeTransferableSkills(
      unmatchedResumeSkills,
      unmatchedJobSkills,
      options
    );
  }
}
