import { RunnableSequence } from '@langchain/core/runnables';

import type { Job } from '../../../entities/job';
import type {
  ConsolidatedSkill,
  SkillMatchOptions,
  TransferableSkillMatch,
} from '../types';
import { withTimeout } from '../utils';
import { BaseExtractor } from './base-extractor';

/**
 * Handles single-call extraction of transferable skills using AI.
 * This approach processes all skills in one LLM call for efficiency.
 */
export class SingleExtractor extends BaseExtractor {
  /**
   * Processes all skill comparisons in a single AI call
   */
  async extractTransferableSkills(
    unmatchedResumeSkills: ConsolidatedSkill[],
    unmatchedJobSkills: Job['skills'],
    options: SkillMatchOptions
  ): Promise<TransferableSkillMatch[]> {
    if (!unmatchedJobSkills?.length || !unmatchedResumeSkills.length) {
      return [];
    }

    // Format all resume skills for the prompt
    const resumeSkillsText = unmatchedResumeSkills
      .map(
        (skill) =>
          `- ${skill.name}${
            skill.keywords?.length ? ` (${skill.keywords.join(', ')})` : ''
          }`
      )
      .join('\n');

    // Format all job skills for the prompt
    const jobSkillsText = unmatchedJobSkills
      .map(
        (skill) =>
          `- ${skill.name}${
            skill.keywords?.length ? ` (${skill.keywords.join(', ')})` : ''
          }`
      )
      .join('\n');

    // Create the runnable sequence
    const chain = RunnableSequence.from([
      this.promptTemplate,
      this.model,
      this.outputParser,
    ]);

    // Execute the AI analysis
    const chainOperation = chain.invoke({
      resumeSkills: resumeSkillsText,
      jobSkills: jobSkillsText,
      formatInstructions: this.outputParser.getFormatInstructions(),
    });

    const result = options.timeoutMs
      ? await withTimeout(
          chainOperation,
          options.timeoutMs,
          'Transferable skill AI analysis'
        )
      : await chainOperation;

    // Convert AI results to TransferableSkillMatch format with validation
    return result.matches
      .filter((match) => this.validateMatch(match, options))
      .map((match) => {
        // Find the source resume information
        const resumeSkill = unmatchedResumeSkills.find(
          (skill) => skill.name === match.resumeSkill
        );
        const sourceResume =
          options.includeSourceResume && resumeSkill?.sourceResumes.length
            ? resumeSkill.sourceResumes[0]
            : undefined;

        return {
          jobSkill: match.jobSkill,
          resumeSkill: match.resumeSkill,
          confidenceRating: match.confidenceRating,
          reasoning: this.validateAndCleanReasoning(match.reasoning),
          sourceResume,
        };
      });
  }
}
