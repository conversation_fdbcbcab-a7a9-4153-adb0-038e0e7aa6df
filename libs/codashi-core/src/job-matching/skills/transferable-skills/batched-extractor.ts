import type { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { RunnableSequence } from '@langchain/core/runnables';

import type { Job } from '../../../entities/job';
import type {
  ConsolidatedSkill,
  SkillMatchOptions,
  TransferableSkillMatch,
} from '../types';
import { isAIUnavailableError, isTimeoutError, withTimeout } from '../utils';

import { BaseExtractor } from './base-extractor';

/**
 * Internal interface for skill comparison pairs
 */
interface SkillComparison {
  resumeSkill: ConsolidatedSkill;
  jobSkill: NonNullable<Job['skills']>[number];
}

/**
 * Handles batched extraction of transferable skills using AI.
 * This approach processes skills in smaller chunks for large inputs.
 */
export class BatchedExtractor extends BaseExtractor {
  // Configuration constants
  private readonly LARGE_BATCH_SIZE = 50;

  constructor(model: BaseChatModel) {
    super(model);
  }

  /**
   * Fallback method for large inputs that exceed token limits
   * Uses larger batches than the original implementation for better efficiency
   */
  async extractTransferableSkills(
    unmatchedResumeSkills: ConsolidatedSkill[],
    unmatchedJobSkills: Job['skills'],
    options: SkillMatchOptions
  ): Promise<TransferableSkillMatch[]> {
    // Create skill comparison pairs
    const skillComparisons = this.createSkillComparisons(
      unmatchedResumeSkills,
      unmatchedJobSkills
    );

    if (!skillComparisons.length) {
      return [];
    }

    // Use larger batches for fallback (50 instead of 10)
    const batches: SkillComparison[][] = [];

    for (let i = 0; i < skillComparisons.length; i += this.LARGE_BATCH_SIZE) {
      batches.push(skillComparisons.slice(i, i + this.LARGE_BATCH_SIZE));
    }

    const allMatches: TransferableSkillMatch[] = [];

    for (const batch of batches) {
      try {
        const batchMatches = await this.processBatch(batch, options);
        allMatches.push(...batchMatches);
      } catch (error) {
        if (isAIUnavailableError(error)) {
          console.warn(
            'AI model unavailable for transferable skill batch, skipping batch'
          );
        } else if (isTimeoutError(error)) {
          console.warn('Transferable skill batch timed out, skipping batch');
        } else {
          console.warn('Failed to process transferable skill batch:', error);
        }
        // Continue with other batches on failure
      }
    }

    return allMatches;
  }

  /**
   * Creates skill comparison pairs from unmatched skills with deduplication
   */
  private createSkillComparisons(
    resumeSkills: ConsolidatedSkill[],
    jobSkills: Job['skills']
  ): SkillComparison[] {
    const comparisons: SkillComparison[] = [];
    const seenPairs = new Set<string>();

    for (const resumeSkill of resumeSkills) {
      if (!jobSkills) {
        continue;
      }

      for (const jobSkill of jobSkills) {
        // Create a normalized key for deduplication
        const pairKey = this.createSkillPairKey(
          resumeSkill.name,
          jobSkill.name
        );

        // Skip if we've already seen this skill pair
        if (seenPairs.has(pairKey)) {
          continue;
        }

        seenPairs.add(pairKey);
        comparisons.push({
          resumeSkill,
          jobSkill,
        });
      }
    }

    // No grouping or optimization: return all unique pairs
    return comparisons;
  }

  /**
   * Processes a batch of skill comparisons using AI
   */
  private async processBatch(
    batch: SkillComparison[],
    options: SkillMatchOptions
  ): Promise<TransferableSkillMatch[]> {
    // Format skills for the prompt
    const resumeSkillsText = batch
      .map(
        ({ resumeSkill }) =>
          `- ${resumeSkill.name}${
            resumeSkill.keywords?.length
              ? ` (${resumeSkill.keywords.join(', ')})`
              : ''
          }`
      )
      .join('\n');

    const jobSkillsText = batch
      .map(
        ({ jobSkill }) =>
          `- ${jobSkill.name}${
            jobSkill.keywords?.length
              ? ` (${jobSkill.keywords.join(', ')})`
              : ''
          }`
      )
      .join('\n');

    // Create the runnable sequence
    const chain = RunnableSequence.from([
      this.promptTemplate,
      this.model,
      this.outputParser,
    ]);

    // Execute the AI analysis
    const chainOperation = chain.invoke({
      resumeSkills: resumeSkillsText,
      jobSkills: jobSkillsText,
      formatInstructions: this.outputParser.getFormatInstructions(),
    });

    const result = options.timeoutMs
      ? await withTimeout(
          chainOperation,
          options.timeoutMs,
          'Transferable skill AI analysis'
        )
      : await chainOperation;

    // Convert AI results to TransferableSkillMatch format with validation
    return result.matches
      .filter((match) => this.validateMatch(match, options))
      .map((match) => {
        // Find the source resume information
        const resumeSkill = batch.find(
          (b) => b.resumeSkill.name === match.resumeSkill
        )?.resumeSkill;
        const sourceResume =
          options.includeSourceResume && resumeSkill?.sourceResumes.length
            ? resumeSkill.sourceResumes[0]
            : undefined;

        return {
          jobSkill: match.jobSkill,
          resumeSkill: match.resumeSkill,
          confidenceRating: match.confidenceRating,
          reasoning: this.validateAndCleanReasoning(match.reasoning),
          sourceResume,
        };
      });
  }

  /**
   * Creates a normalized key for skill pair deduplication
   */
  private createSkillPairKey(resumeSkill: string, jobSkill: string): string {
    // Normalize skill names for comparison (lowercase, trim)
    const normalizedResume = resumeSkill.toLowerCase().trim();
    const normalizedJob = jobSkill.toLowerCase().trim();

    // Create a consistent key regardless of order
    return `${normalizedResume}|${normalizedJob}`;
  }
}
