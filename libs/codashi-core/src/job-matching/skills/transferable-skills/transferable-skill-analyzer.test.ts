import { FakeListChatModel } from '@langchain/core/utils/testing';
import { beforeEach, describe, expect, it } from 'vitest';

import type { Job } from '../../../entities/job';
import type { ConsolidatedSkill } from '../types';
import { TransferableSkillAnalyzer } from './transferable-skill-analyzer';

describe('TransferableSkillAnalyzer', () => {
  let analyzer: TransferableSkillAnalyzer;
  let fakeModel: FakeListChatModel;

  beforeEach(() => {
    fakeModel = new FakeListChatModel({
      responses: [],
    });

    analyzer = new TransferableSkillAnalyzer(fakeModel);
  });

  describe('analyzeTransferableSkills', () => {
    it('should return empty array when no unmatched resume skills', async () => {
      const unmatchedResumeSkills: ConsolidatedSkill[] = [];
      const unmatchedJobSkills: Job['skills'] = [
        { name: 'React', level: null, keywords: ['JSX', 'Hooks'] },
      ];

      const result = await analyzer.analyzeTransferableSkills(
        unmatchedResumeSkills,
        unmatchedJobSkills
      );

      expect(result).toEqual([]);
    });

    it('should return empty array when no unmatched job skills', async () => {
      const unmatchedResumeSkills: ConsolidatedSkill[] = [
        {
          name: 'Vue',
          level: 'Intermediate',
          keywords: ['Vue.js'],
          sourceResumes: [0],
        },
      ];
      const unmatchedJobSkills: Job['skills'] = [];

      const result = await analyzer.analyzeTransferableSkills(
        unmatchedResumeSkills,
        unmatchedJobSkills
      );

      expect(result).toEqual([]);
    });

    it('should analyze transferable skills and return matches', async () => {
      const unmatchedResumeSkills: ConsolidatedSkill[] = [
        {
          name: 'Vue',
          level: 'Intermediate',
          keywords: ['Vue.js'],
          sourceResumes: [0],
        },
        {
          name: 'MySQL',
          level: 'Advanced',
          keywords: ['SQL'],
          sourceResumes: [1],
        },
      ];

      const unmatchedJobSkills: Job['skills'] = [
        { name: 'React', level: null, keywords: ['JSX', 'Hooks'] },
        { name: 'PostgreSQL', level: null, keywords: ['SQL'] },
      ];

      // Configure fake model with AI response
      fakeModel.responses = [
        JSON.stringify({
          matches: [
            {
              resumeSkill: 'Vue',
              jobSkill: 'React',
              confidenceRating: 2,
              reasoning:
                'Both are modern frontend frameworks with similar component-based architecture',
            },
            {
              resumeSkill: 'MySQL',
              jobSkill: 'PostgreSQL',
              confidenceRating: 3,
              reasoning:
                'Both are relational databases with similar SQL syntax',
            },
          ],
        }),
      ];

      const result = await analyzer.analyzeTransferableSkills(
        unmatchedResumeSkills,
        unmatchedJobSkills
      );

      expect(result).toHaveLength(2);
      expect(result[0]).toEqual({
        jobSkill: 'PostgreSQL',
        resumeSkill: 'MySQL',
        confidenceRating: 3,
        reasoning: 'Both are relational databases with similar SQL syntax.',
        sourceResume: undefined,
      });
      expect(result[1]).toEqual({
        jobSkill: 'React',
        resumeSkill: 'Vue',
        confidenceRating: 2,
        reasoning:
          'Both are modern frontend frameworks with similar component-based architecture.',
        sourceResume: undefined,
      });
    });

    it('should handle AI processing errors gracefully', async () => {
      const unmatchedResumeSkills: ConsolidatedSkill[] = [
        {
          name: 'Vue',
          level: 'Intermediate',
          keywords: ['Vue.js'],
          sourceResumes: [0],
        },
      ];

      const unmatchedJobSkills: Job['skills'] = [
        { name: 'React', level: null, keywords: ['JSX'] },
      ];

      // Configure fake model with invalid JSON to trigger error
      fakeModel.responses = ['invalid json response'];

      const result = await analyzer.analyzeTransferableSkills(
        unmatchedResumeSkills,
        unmatchedJobSkills
      );

      expect(result).toEqual([]);
    });

    it('should filter out matches with confidence threshold', async () => {
      const unmatchedResumeSkills: ConsolidatedSkill[] = [
        {
          name: 'Vue',
          level: 'Intermediate',
          keywords: ['Vue.js'],
          sourceResumes: [0],
        },
        {
          name: 'MySQL',
          level: 'Advanced',
          keywords: ['SQL'],
          sourceResumes: [0],
        },
      ];

      const unmatchedJobSkills: Job['skills'] = [
        { name: 'React', level: null, keywords: ['JSX'] },
        { name: 'PostgreSQL', level: null, keywords: ['SQL'] },
      ];

      // Configure fake model with AI response containing different confidence ratings
      fakeModel.responses = [
        JSON.stringify({
          matches: [
            {
              resumeSkill: 'Vue',
              jobSkill: 'React',
              confidenceRating: 1, // Low confidence
              reasoning:
                'Both are frontend frameworks with different architectures',
            },
            {
              resumeSkill: 'MySQL',
              jobSkill: 'PostgreSQL',
              confidenceRating: 3, // High confidence
              reasoning:
                'Both are relational databases with similar SQL syntax',
            },
          ],
        }),
      ];

      const result = await analyzer.analyzeTransferableSkills(
        unmatchedResumeSkills,
        unmatchedJobSkills,
        { confidenceThreshold: 2 } // Only include matches with confidence >= 2
      );

      // Should only include the high confidence match
      expect(result).toHaveLength(1);
      expect(result[0].resumeSkill).toBe('MySQL');
      expect(result[0].confidenceRating).toBe(3);
    });

    it('should filter out matches with invalid reasoning', async () => {
      const unmatchedResumeSkills: ConsolidatedSkill[] = [
        {
          name: 'Vue',
          level: 'Intermediate',
          keywords: ['Vue.js'],
          sourceResumes: [0],
        },
        {
          name: 'MySQL',
          level: 'Advanced',
          keywords: ['SQL'],
          sourceResumes: [0],
        },
      ];

      const unmatchedJobSkills: Job['skills'] = [
        { name: 'React', level: null, keywords: ['JSX'] },
        { name: 'PostgreSQL', level: null, keywords: ['SQL'] },
      ];

      // Configure fake model with AI response containing invalid reasoning
      fakeModel.responses = [
        JSON.stringify({
          matches: [
            {
              resumeSkill: 'Vue',
              jobSkill: 'React',
              confidenceRating: 2,
              reasoning: 'similar', // Too generic/short
            },
            {
              resumeSkill: 'MySQL',
              jobSkill: 'PostgreSQL',
              confidenceRating: 3,
              reasoning:
                'Both are relational database management systems with similar SQL syntax and functionality',
            },
          ],
        }),
      ];

      const result = await analyzer.analyzeTransferableSkills(
        unmatchedResumeSkills,
        unmatchedJobSkills
      );

      // Should only include the match with valid reasoning
      expect(result).toHaveLength(1);
      expect(result[0].resumeSkill).toBe('MySQL');
      expect(result[0].reasoning).toBe(
        'Both are relational database management systems with similar SQL syntax and functionality.'
      );
    });

    it('should clean and format reasoning text', async () => {
      const unmatchedResumeSkills: ConsolidatedSkill[] = [
        {
          name: 'Vue',
          level: 'Intermediate',
          keywords: ['Vue.js'],
          sourceResumes: [0],
        },
      ];

      const unmatchedJobSkills: Job['skills'] = [
        { name: 'React', level: null, keywords: ['JSX'] },
      ];

      // Configure fake model with AI response containing reasoning that needs cleaning
      fakeModel.responses = [
        JSON.stringify({
          matches: [
            {
              resumeSkill: 'Vue',
              jobSkill: 'React',
              confidenceRating: 2,
              reasoning:
                '  both are modern frontend frameworks with component-based architecture  ', // Needs trimming and capitalization
            },
          ],
        }),
      ];

      const result = await analyzer.analyzeTransferableSkills(
        unmatchedResumeSkills,
        unmatchedJobSkills
      );

      expect(result).toHaveLength(1);
      expect(result[0].reasoning).toBe(
        'Both are modern frontend frameworks with component-based architecture.'
      );
    });

    it('should deduplicate similar skill comparisons', async () => {
      const unmatchedResumeSkills: ConsolidatedSkill[] = [
        {
          name: 'Vue',
          level: 'Intermediate',
          keywords: ['Vue.js'],
          sourceResumes: [0],
        },
        {
          name: 'Vue.js',
          level: 'Advanced',
          keywords: ['Vue'],
          sourceResumes: [1],
        }, // Similar to above
      ];

      const unmatchedJobSkills: Job['skills'] = [
        { name: 'React', level: null, keywords: ['JSX'] },
      ];

      // Configure fake model with AI response
      fakeModel.responses = [
        JSON.stringify({
          matches: [
            {
              resumeSkill: 'Vue',
              jobSkill: 'React',
              confidenceRating: 2,
              reasoning: 'Both are frontend frameworks',
            },
          ],
        }),
      ];

      const result = await analyzer.analyzeTransferableSkills(
        unmatchedResumeSkills,
        unmatchedJobSkills
      );

      // Should process and return results (deduplication happens internally)
      expect(result).toHaveLength(1);
      expect(result[0].resumeSkill).toBe('Vue');
      expect(result[0].jobSkill).toBe('React');
    });

    it('should optimize skill comparisons by priority', async () => {
      const unmatchedResumeSkills: ConsolidatedSkill[] = [
        {
          name: 'Vue',
          level: 'Intermediate',
          keywords: ['Vue.js', 'Components'],
          sourceResumes: [0],
        },
        {
          name: 'MySQL',
          level: 'Advanced',
          keywords: ['SQL', 'Database'],
          sourceResumes: [0],
        },
      ];

      const unmatchedJobSkills: Job['skills'] = [
        { name: 'React', level: null, keywords: ['JSX', 'Components'] }, // Has keyword overlap with Vue
        { name: 'PostgreSQL', level: null, keywords: ['SQL'] }, // Has keyword overlap with MySQL
      ];

      // Configure fake model with AI response
      fakeModel.responses = [
        JSON.stringify({
          matches: [
            {
              resumeSkill: 'Vue',
              jobSkill: 'React',
              confidenceRating: 2,
              reasoning:
                'Both are frontend frameworks with component-based architecture',
            },
            {
              resumeSkill: 'MySQL',
              jobSkill: 'PostgreSQL',
              confidenceRating: 3,
              reasoning: 'Both are relational databases with SQL support',
            },
          ],
        }),
      ];

      const result = await analyzer.analyzeTransferableSkills(
        unmatchedResumeSkills,
        unmatchedJobSkills
      );

      expect(result).toHaveLength(2);
      // Results should be sorted by confidence rating (highest first)
      expect(result[0].confidenceRating).toBe(3);
      expect(result[1].confidenceRating).toBe(2);
    });

    it('should include sourceResume when includeSourceResume option is set', async () => {
      const unmatchedResumeSkills: ConsolidatedSkill[] = [
        {
          name: 'Vue',
          level: 'Intermediate',
          keywords: ['Vue.js'],
          sourceResumes: [1],
        },
      ];
      const unmatchedJobSkills: Job['skills'] = [
        { name: 'React', level: null, keywords: ['JSX'] },
      ];
      fakeModel.responses = [
        JSON.stringify({
          matches: [
            {
              resumeSkill: 'Vue',
              jobSkill: 'React',
              confidenceRating: 2,
              reasoning: 'Both are frontend frameworks',
            },
          ],
        }),
      ];
      const result = await analyzer.analyzeTransferableSkills(
        unmatchedResumeSkills,
        unmatchedJobSkills,
        { includeSourceResume: true }
      );
      expect(result).toHaveLength(1);
      expect(result[0].sourceResume).toBe(1);
    });

    it('should limit the number of results if maxTransferableSkills is set', async () => {
      const unmatchedResumeSkills: ConsolidatedSkill[] = [
        {
          name: 'Vue',
          level: 'Intermediate',
          keywords: ['Vue.js'],
          sourceResumes: [0],
        },
        {
          name: 'MySQL',
          level: 'Advanced',
          keywords: ['SQL'],
          sourceResumes: [0],
        },
      ];
      const unmatchedJobSkills: Job['skills'] = [
        { name: 'React', level: null, keywords: ['JSX'] },
        { name: 'PostgreSQL', level: null, keywords: ['SQL'] },
      ];
      fakeModel.responses = [
        JSON.stringify({
          matches: [
            {
              resumeSkill: 'Vue',
              jobSkill: 'React',
              confidenceRating: 2,
              reasoning: 'Both are frontend frameworks',
            },
            {
              resumeSkill: 'MySQL',
              jobSkill: 'PostgreSQL',
              confidenceRating: 3,
              reasoning: 'Both are relational databases',
            },
          ],
        }),
      ];
      const result = await analyzer.analyzeTransferableSkills(
        unmatchedResumeSkills,
        unmatchedJobSkills,
        { maxTransferableSkills: 1 }
      );
      expect(result).toHaveLength(1);
    });
  });

  describe('Performance Validation', () => {
    describe('Single-Call Strategy Validation', () => {
      it('should use single-call approach for typical skill sets', async () => {
        const mockModel = new FakeListChatModel({
          responses: [
            JSON.stringify({
              matches: [
                {
                  resumeSkill: 'Vue.js',
                  jobSkill: 'React',
                  confidenceRating: 2,
                  reasoning:
                    'Both are modern frontend frameworks with component-based architecture and similar concepts like state management and lifecycle methods',
                },
                {
                  resumeSkill: 'MySQL',
                  jobSkill: 'PostgreSQL',
                  confidenceRating: 3,
                  reasoning:
                    'Both are relational databases with very similar SQL syntax and database management concepts',
                },
                {
                  resumeSkill: 'Express.js',
                  jobSkill: 'FastAPI',
                  confidenceRating: 2,
                  reasoning:
                    'Both are web frameworks for building APIs, though Express is for Node.js and FastAPI is for Python',
                },
              ],
            }),
          ],
        });

        const analyzer = new TransferableSkillAnalyzer(mockModel);

        // Typical skill set (should use single-call approach)
        const unmatchedResumeSkills: ConsolidatedSkill[] = [
          { name: 'Vue.js', keywords: ['Vue', 'Vuex'], sourceResumes: [0] },
          { name: 'MySQL', keywords: ['Database', 'SQL'], sourceResumes: [0] },
          {
            name: 'Express.js',
            keywords: ['Node.js', 'API'],
            sourceResumes: [0],
          },
          {
            name: 'Docker',
            keywords: ['Containerization'],
            sourceResumes: [0],
          },
          { name: 'Git', keywords: ['Version Control'], sourceResumes: [0] },
        ];

        const unmatchedJobSkills: Job['skills'] = [
          { name: 'React', level: null, keywords: ['JSX', 'Hooks'] },
          { name: 'PostgreSQL', level: null, keywords: ['Database'] },
          { name: 'FastAPI', level: null, keywords: ['Python', 'API'] },
          { name: 'Kubernetes', level: null, keywords: ['Orchestration'] },
        ];

        const result = await analyzer.analyzeTransferableSkills(
          unmatchedResumeSkills,
          unmatchedJobSkills
        );

        // Validate results (sorted by confidence rating descending)
        expect(result).toHaveLength(3);
        expect(result[0].confidenceRating).toBe(3);
        expect(result[0].reasoning).toContain('relational databases');
        expect(result[1].confidenceRating).toBe(2);
        expect(result[1].reasoning).toContain('frontend frameworks');
        expect(result[2].confidenceRating).toBe(2);
        expect(result[2].reasoning).toContain('web frameworks');

        // Verify all matches have proper structure
        result.forEach((match) => {
          expect(match.jobSkill).toBeDefined();
          expect(match.resumeSkill).toBeDefined();
          expect(match.confidenceRating).toBeGreaterThanOrEqual(1);
          expect(match.confidenceRating).toBeLessThanOrEqual(3);
          expect(match.reasoning).toBeDefined();
          expect(match.reasoning.length).toBeGreaterThan(10);
        });
      });

      it('should handle large skill sets with fallback strategy', async () => {
        const mockModel = new FakeListChatModel({
          responses: [
            // Multiple responses for batch processing fallback
            JSON.stringify({
              matches: [
                {
                  resumeSkill: 'Skill1',
                  jobSkill: 'JobSkill1',
                  confidenceRating: 2,
                  reasoning: 'Related technologies with similar concepts',
                },
              ],
            }),
            JSON.stringify({
              matches: [
                {
                  resumeSkill: 'Skill2',
                  jobSkill: 'JobSkill2',
                  confidenceRating: 3,
                  reasoning:
                    'Very similar technologies with high transferability',
                },
              ],
            }),
          ],
        });

        const analyzer = new TransferableSkillAnalyzer(mockModel);

        // Create a large skill set that should trigger fallback
        const unmatchedResumeSkills: ConsolidatedSkill[] = Array.from(
          { length: 100 },
          (_, i) => ({
            name: `ResumeSkill${i + 1}`,
            keywords: [`Keyword${i + 1}`, `Related${i + 1}`],
            sourceResumes: [0],
          })
        );

        const unmatchedJobSkills: Job['skills'] = Array.from(
          { length: 100 },
          (_, i) => ({
            name: `JobSkill${i + 1}`,
            level: null,
            keywords: [`JobKeyword${i + 1}`],
          })
        );

        const result = await analyzer.analyzeTransferableSkills(
          unmatchedResumeSkills,
          unmatchedJobSkills
        );

        // Should handle large inputs gracefully
        expect(result).toBeDefined();
        expect(Array.isArray(result)).toBe(true);

        // Validate any returned matches have proper structure
        result.forEach((match) => {
          expect(match.jobSkill).toBeDefined();
          expect(match.resumeSkill).toBeDefined();
          expect(match.confidenceRating).toBeGreaterThanOrEqual(1);
          expect(match.confidenceRating).toBeLessThanOrEqual(3);
          expect(match.reasoning).toBeDefined();
        });
      });
    });

    describe('Result Quality Validation', () => {
      it('should produce high-quality reasoning for transferable skills', async () => {
        const mockModel = new FakeListChatModel({
          responses: [
            JSON.stringify({
              matches: [
                {
                  resumeSkill: 'Angular',
                  jobSkill: 'React',
                  confidenceRating: 2,
                  reasoning:
                    'Both Angular and React are modern frontend frameworks that use component-based architecture. While Angular uses TypeScript by default.',
                },
                {
                  resumeSkill: 'Oracle Database',
                  jobSkill: 'MongoDB',
                  confidenceRating: 1,
                  reasoning:
                    'Oracle Database is a relational database while MongoDB is a NoSQL document database with different paradigms.',
                },
              ],
            }),
          ],
        });

        const analyzer = new TransferableSkillAnalyzer(mockModel);

        const unmatchedResumeSkills: ConsolidatedSkill[] = [
          {
            name: 'Angular',
            keywords: ['TypeScript', 'SPA'],
            sourceResumes: [0],
          },
          {
            name: 'Oracle Database',
            keywords: ['SQL', 'RDBMS'],
            sourceResumes: [0],
          },
        ];

        const unmatchedJobSkills: Job['skills'] = [
          { name: 'React', level: null, keywords: ['JSX', 'JavaScript'] },
          { name: 'MongoDB', level: null, keywords: ['NoSQL', 'Document'] },
        ];

        const result = await analyzer.analyzeTransferableSkills(
          unmatchedResumeSkills,
          unmatchedJobSkills
        );

        expect(result).toHaveLength(2);

        // Validate high-confidence match has detailed reasoning
        const highConfidenceMatch = result.find(
          (m) => m.confidenceRating === 2
        );
        expect(highConfidenceMatch).toBeDefined();
        if (highConfidenceMatch) {
          expect(highConfidenceMatch.reasoning).toContain(
            'component-based architecture'
          );
          expect(highConfidenceMatch.reasoning.length).toBeGreaterThan(50);
        }

        // Validate low-confidence match explains differences
        const lowConfidenceMatch = result.find((m) => m.confidenceRating === 1);
        expect(lowConfidenceMatch).toBeDefined();
        if (lowConfidenceMatch) {
          expect(lowConfidenceMatch.reasoning).toContain('different');
          expect(lowConfidenceMatch.reasoning.length).toBeGreaterThan(50);
        }
      });

      it('should handle confidence threshold filtering correctly', async () => {
        const mockModel = new FakeListChatModel({
          responses: [
            JSON.stringify({
              matches: [
                {
                  resumeSkill: 'Python',
                  jobSkill: 'Java',
                  confidenceRating: 2,
                  reasoning:
                    'Both are object-oriented programming languages with similar concepts',
                },
                {
                  resumeSkill: 'Photoshop',
                  jobSkill: 'Programming',
                  confidenceRating: 1,
                  reasoning:
                    'Very different skill domains with minimal transferability',
                },
                {
                  resumeSkill: 'AWS',
                  jobSkill: 'Azure',
                  confidenceRating: 3,
                  reasoning:
                    'Both are cloud platforms with very similar services and concepts',
                },
              ],
            }),
          ],
        });

        const analyzer = new TransferableSkillAnalyzer(mockModel);

        const unmatchedResumeSkills: ConsolidatedSkill[] = [
          { name: 'Python', keywords: ['Programming'], sourceResumes: [0] },
          { name: 'Photoshop', keywords: ['Design'], sourceResumes: [0] },
          { name: 'AWS', keywords: ['Cloud'], sourceResumes: [0] },
        ];

        const unmatchedJobSkills: Job['skills'] = [
          { name: 'Java', level: null, keywords: ['Programming'] },
          { name: 'Programming', level: null, keywords: ['Code'] },
          { name: 'Azure', level: null, keywords: ['Cloud'] },
        ];

        // Test with confidence threshold of 2
        const result = await analyzer.analyzeTransferableSkills(
          unmatchedResumeSkills,
          unmatchedJobSkills,
          { confidenceThreshold: 2 }
        );

        // Should only include matches with confidence >= 2
        expect(result).toHaveLength(2);
        result.forEach((match) => {
          expect(match.confidenceRating).toBeGreaterThanOrEqual(2);
        });

        // Verify specific matches
        expect(result.some((m) => m.resumeSkill === 'Python')).toBe(true);
        expect(result.some((m) => m.resumeSkill === 'AWS')).toBe(true);
        expect(result.some((m) => m.resumeSkill === 'Photoshop')).toBe(false);
      });

      it('should respect maxTransferableSkills limit', async () => {
        const mockModel = new FakeListChatModel({
          responses: [
            JSON.stringify({
              matches: [
                {
                  resumeSkill: 'Skill1',
                  jobSkill: 'Job1',
                  confidenceRating: 3,
                  reasoning: 'High confidence match',
                },
                {
                  resumeSkill: 'Skill2',
                  jobSkill: 'Job2',
                  confidenceRating: 2,
                  reasoning: 'Medium confidence match',
                },
                {
                  resumeSkill: 'Skill3',
                  jobSkill: 'Job3',
                  confidenceRating: 2,
                  reasoning: 'Another medium confidence match',
                },
                {
                  resumeSkill: 'Skill4',
                  jobSkill: 'Job4',
                  confidenceRating: 1,
                  reasoning: 'Low confidence match',
                },
              ],
            }),
          ],
        });

        const analyzer = new TransferableSkillAnalyzer(mockModel);

        const unmatchedResumeSkills: ConsolidatedSkill[] = [
          { name: 'Skill1', keywords: [], sourceResumes: [0] },
          { name: 'Skill2', keywords: [], sourceResumes: [0] },
          { name: 'Skill3', keywords: [], sourceResumes: [0] },
          { name: 'Skill4', keywords: [], sourceResumes: [0] },
        ];

        const unmatchedJobSkills: Job['skills'] = [
          { name: 'Job1', level: null, keywords: [] },
          { name: 'Job2', level: null, keywords: [] },
          { name: 'Job3', level: null, keywords: [] },
          { name: 'Job4', level: null, keywords: [] },
        ];

        const result = await analyzer.analyzeTransferableSkills(
          unmatchedResumeSkills,
          unmatchedJobSkills,
          { maxTransferableSkills: 2 }
        );

        // Should limit to 2 results and prioritize by confidence
        expect(result).toHaveLength(2);
        expect(result[0].confidenceRating).toBe(3); // Highest confidence first
        expect(result[1].confidenceRating).toBe(2); // Second highest
      });
    });

    describe('Edge Case Validation', () => {
      it('should handle empty skill sets gracefully', async () => {
        const mockModel = new FakeListChatModel({ responses: [] });
        const analyzer = new TransferableSkillAnalyzer(mockModel);

        const result1 = await analyzer.analyzeTransferableSkills([], []);
        expect(result1).toEqual([]);

        const result2 = await analyzer.analyzeTransferableSkills(
          [{ name: 'Skill', keywords: [], sourceResumes: [0] }],
          []
        );
        expect(result2).toEqual([]);

        const result3 = await analyzer.analyzeTransferableSkills(
          [],
          [{ name: 'Job', level: null, keywords: [] }]
        );
        expect(result3).toEqual([]);
      });

      it('should handle includeSourceResume option correctly', async () => {
        const mockModel = new FakeListChatModel({
          responses: [
            JSON.stringify({
              matches: [
                {
                  resumeSkill: 'TestSkill',
                  jobSkill: 'TestJob',
                  confidenceRating: 2,
                  reasoning: 'Test reasoning',
                },
              ],
            }),
          ],
        });

        const analyzer = new TransferableSkillAnalyzer(mockModel);

        const unmatchedResumeSkills: ConsolidatedSkill[] = [
          { name: 'TestSkill', keywords: [], sourceResumes: [1] },
        ];

        const unmatchedJobSkills: Job['skills'] = [
          { name: 'TestJob', level: null, keywords: [] },
        ];

        // Test with includeSourceResume: true
        const resultWithSource = await analyzer.analyzeTransferableSkills(
          unmatchedResumeSkills,
          unmatchedJobSkills,
          { includeSourceResume: true }
        );

        expect(resultWithSource).toHaveLength(1);
        expect(resultWithSource[0].sourceResume).toBe(1);

        // Test with includeSourceResume: false (default)
        const resultWithoutSource = await analyzer.analyzeTransferableSkills(
          unmatchedResumeSkills,
          unmatchedJobSkills,
          { includeSourceResume: false }
        );

        expect(resultWithoutSource).toHaveLength(1);
        expect(resultWithoutSource[0].sourceResume).toBeUndefined();
      });
    });
  });
});
